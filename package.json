{"name": "propvr-ui-overlay", "public": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "husky help &&  vite", "dev:prod": "husky help && vite --mode prod", "dev:uat": "husky help && vite --mode uat", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "build:prestige": "vite build --mode prestige", "start": "serve -s dist -p 4005", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "start-storybook": "node serve-storybook.js", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "prepare": "husky", "postinstall": "patch-package"}, "dependencies": {"@babel/eslint-parser": "^7.23.3", "@google/maps": "^1.1.3", "@lumaai/luma-web": "^0.2.2", "@zag-js/slider": "^0.77.1", "@zag-js/vue": "^0.77.1", "axios": "^1.5.0", "axois": "^0.0.1-security", "browserslist": "^4.22.3", "crypto-js": "^4.2.0", "express": "^4.18.2", "flowbite-vue": "^0.1.5", "gsap": "^3.13.0", "ogl": "^1.0.11", "openseadragon": "^4.1.1", "pako": "^2.1.0", "patch-package": "^8.0.0", "pinia": "^2.1.4", "portal-vue": "^3.0.0", "postinstall-postinstall": "^2.1.0", "propvr-globe-component": "^0.0.18", "propvr-svg-overlay": "^0.0.57", "router": "^1.3.8", "scss": "^0.2.4", "serve": "^14.2.0", "svg-pan-zoom": "^3.6.1", "swiper": "^11.0.5", "three": "^0.157.0", "three-globe": "^2.30.0", "tweenjs": "^1.0.2", "vee-validate": "^4.12.2", "video.js": "^8.12.0", "videojs-panorama": "^0.1.7", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-devtools": "^7.7.7", "vue": "^3.3.4", "vue-3-slider-component": "^1.0.0", "vue-pannellum": "^0.5.3", "vue-router": "^4.2.4", "yup": "^1.3.2"}, "devDependencies": {"@storybook/addon-designs": "^7.0.7", "@storybook/addon-essentials": "^7.6.4", "@storybook/addon-interactions": "^7.6.4", "@storybook/addon-links": "^7.6.4", "@storybook/blocks": "^7.6.4", "@storybook/test": "^7.6.4", "@storybook/vue3": "^7.6.4", "@storybook/vue3-vite": "^7.6.4", "@types/google.maps": "^3.55.9", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jest": "^27.6.3", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-vue": "^9.21.0", "husky": "^9.0.10", "postcss": "^8.4.25", "prettier": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.77.8", "sass-loader": "^14.2.1", "storybook": "^7.6.4", "tailwindcss": "^3.3.3", "vite": "^4.4.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "browserslist": {"production": [">0.2%", "not ie <= 11", "not op_mini all"]}}