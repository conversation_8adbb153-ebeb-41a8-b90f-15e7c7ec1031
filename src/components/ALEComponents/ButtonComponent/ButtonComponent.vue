<template>
  <div class="flex flex-col gap-4">
    <fwb-button
      class=" buttons z-[4] !bg-primary rounded-tl-lg rounded-br-lg rounded-tr-lg rounded-bl-lg justify-center items-center gap-2 hover:bg-primary hover:textfont focus:ring-0 "
      :class="Store.isMobile? 'w-full': Store.isLandscape ? 'w-[155px]' : ' flex w-[17.875rem]  '"
      @click="handleClick()"
    >
      <span class="text-base not-italic text-primaryText font-medium ">
        <TranslationComp
          :text=" message"
        />

      </span>
      <template #suffix>
        <div
          class="buttonsvg"
          v-html="svg"
        />
      </template>
    </fwb-button>
  </div>
</template>

<script setup>
import { FwbButton } from 'flowbite-vue';
import { defineEmits, defineProps } from 'vue';
import { creationToolStore } from '../../../store';
import TranslationComp from '../TranslationComp/TranslationComp.vue';

const Store = creationToolStore();
const emit = defineEmits(['handleButtonClick']);

defineProps({
  message: {
    type: String,
    default: "",
  },
  svg: {
    type: String,
    default: "",
  },
});

const handleClick = () => {
  emit('handleButtonClick');
};

</script>

<style  scoped>
.buttons{
padding: var(--35, 12px) var(--6, 24px);
}

</style>
<style lang="scss">
.buttonsvg svg path{
 fill: var(--primaryText)
}
</style>
