<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import router from '../router';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';
// Import BreadCrumb from '../components/ALEComponents/BreadCrumb/BreadCrumb.vue';
import { cdn, formatBedrooms, Googleanalytics, isRealValue } from '../helpers/helper';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import TourView from '../components/ALEComponents/TourView/TourView.vue';
import HotSpotViewer from '../components/ALEComponents/HotSpotViewer/HotSpotViewer.vue';

// Import UnitPlanFavoriteView from '../components/ALEComponents/UnitPlanFavoriteView/UnitPlanFavoriteView.vue';
import { fetchImageURL } from '../helpers/API';
// import MatterportView from '../components/ALEComponents/Matterport/MatterportView.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
import TabsView from '../components/ALEComponents/TabsView/TabsView.vue';
import DropDown from '../components/ALEComponents/DropDown/DropDown.vue';
import BottomNavigation from '../components/ALEComponents/BottomNavigation/BottomNavigation.vue';
import ButtonGroup from '../components/ALEComponents/ButtonGroup/ButtonGroup.vue';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';
import OverflowSlider from '../components/ALEComponents/OverflowSlider/OverflowSlider.vue';
import NewFloorPlanCard from '../components/ALEComponents/NewFloorPlanCard/NewFloorPlanCard.vue';
// Import RotatableOverlay from '../components/svgOverlay/rotatableOverlay.vue';
import { availUnitOrgs } from '../config/masterdata';

/* Emits & props */
const route = useRoute();
const Store = creationToolStore();
const showUnitplanCard = ref(false);
// Const tabsandNavsData = ['floor plan', '360 tour'];
const selectedTabOption = ref('unitplan');
const itemsRefs = ref({});
const showVrTour = ref(true);
const selectedUnitPlan = ref(), watchedProjectCardData = ref({});
// const loaderProgress = ref(null);
Store.hideLogo=true;
const highResdata = ref(false), lowResData = ref(false), selectedFloorId = ref(), currentImageId = ref(null), startTime = ref(new Date), timespent = ref(0), activeNavigatorTab = ref('unitplan'), showScene = ref(false), activeTab = ref(false), showInterior = ref(false);
const listOfUnitplanNames=ref([]), selectedTypeId = ref(''), scene_data = ref({});
const filteredList = ref([]), selectedMediaIndex = ref(null);
const showMiniMap = ref(Store.isMobile ? false : true), defaultIndex = ref(0);
const currentFloorUnitplandId = ref(null);
const unitPlanArrayData = ref([]), isVisible = ref(false);

// Computed property for isRoot to make it reactive
const isRoot = computed(() => {
  return !Store.getLastVisitedRoute('previous_unitplan');
});

const filteredUnitplanData = computed(() => {
  // If org is not in availUnitOrgs, return all unitplans
  if (!availUnitOrgs.includes(route.params.organizationId)) {
    return Object.fromEntries(
      Object.entries(Store.unitplanData || {}).filter(
        ([_id, unit]) =>
          unit.unit_type !== 'villa_floor',
      ),
    );
  }
  // Otherwise, filter
  const allowedIds = new Set(
    Object.values(Store.listOfVrTourData || {}).map((v) => v.unitplan_id),
  );
  return Object.fromEntries(
    Object.entries(Store.unitplanData || {}).filter(
      ([_id, unit]) =>
        unit.unit_type !== 'villa_floor' && allowedIds.has(unit._id),
    ),
  );
});

const handleCta = (type) => {
  const ctaLink = Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.cta_link;
  if (type==='custom' && ctaLink ){
    const currentEncodedUrl = encodeURIComponent(window.location.href);
    const url = new URL(ctaLink);
    url.searchParams.set("redirect", currentEncodedUrl);
    window.open(url.toString(), "_blank");
  } else if (type==='event_emit'){
    const newObject = {
      actiontype: "ctaClicked",
      unit: Store.unitplanData[route.query.unitplan_id],
    };
    window.parent.postMessage(JSON.stringify(newObject), '*');
  } else if (type==='default'){
    // console.log("type default");
  }
};

const dropDownSvg=`<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0144 13.8334C9.70502 13.8333 9.40833 13.7164 9.18958 13.5085L4.52292 9.0731C4.41149 8.97082 4.32261 8.84846 4.26147 8.71318C4.20032 8.5779 4.16814 8.43239 4.16679 8.28516C4.16545 8.13793 4.19496 7.99192 4.25363 7.85565C4.31229 7.71938 4.39892 7.59557 4.50846 7.49146C4.618 7.38735 4.74826 7.30501 4.89164 7.24926C5.03502 7.19351 5.18864 7.16545 5.34355 7.16673C5.49846 7.16801 5.65155 7.1986 5.79389 7.25671C5.93623 7.31482 6.06496 7.3993 6.17258 7.5052L10.0144 11.1566L13.8562 7.5052C14.0763 7.30322 14.371 7.19145 14.6769 7.19398C14.9828 7.19651 15.2754 7.31312 15.4917 7.51871C15.708 7.7243 15.8307 8.00241 15.8334 8.29314C15.836 8.58388 15.7184 8.86397 15.5059 9.0731L10.8392 13.5085C10.6205 13.7164 10.3238 13.8333 10.0144 13.8334V13.8334Z" fill="black"/>
</svg>`;
if (!Store.organization_thumbnail) {
  Store.getOrganization( route.params.organizationId);
}
if (!Store.sidebarOptions[route.params.projectId]){
  Store.getOptions( route.params.organizationId, route.params.projectId).then((res) => {
    if (Object.values(res).filter((item) => item.type.toLowerCase() === "unitplan")[0]){
      Store.activeOptionId = Object.values(res).filter((item) => item.type.toLowerCase() === "unitplan")[0]._id;
    }
  });
} else {
  if (Object.values(Store.sidebarOptions[route.params.projectId]).filter((item) => item.type.toLowerCase() === "unitplan")[0]){
    Store.activeOptionId = Object.values(Store.sidebarOptions[route.params.projectId]).filter((item) => item.type.toLowerCase() === "unitplan")[0]._id;
  }
}

if (!Store.listOfVrTourData) {
  Store.getListOfVRId(route.params.organizationId, route.params.projectId);
}

if (Object.values(Store.communityData).length === 0){
  Store.getCommunities(route.params.projectId, route.params.organizationId);
}

Store.getTranslation(route.params.organizationId);
Store.updateSceneType(route.fullPath);

const getListOfUnitplanNames = () => {
  listOfUnitplanNames.value = Object.values(filteredUnitplanData.value)
    .map((unit) => unit.name);
};

if (Object.values(filteredUnitplanData.value).length===0){
  Store.getListOfUnitplan(route.params.projectId, route.params.organizationId).then(() => {
    const keys = Object.keys(filteredUnitplanData.value);
    fetchImageURL(cdn(filteredUnitplanData.value[keys[0]]?.thumbnail)).then(() => {
      Object.values(filteredUnitplanData.value).map((item) => {
        return fetchImageURL(cdn(item.thumbnail));
      });
    });
    if (!route.query.unitplan_id) {
      router.push({ query: { unitplan_id: filteredUnitplanData.value[keys[0]]._id, type: route.query.type}});
    } // Set the first index
    selectedUnitPlan.value = filteredUnitplanData.value[route.query.unitplan_id];
    const current = filteredUnitplanData.value[route.query.unitplan_id];
    if (current) {
      if (current.unit_type === 'villa') {
        const floors = current.floor_unitplans || [];
        selectedFloorId.value = floors.length > 0 ? floors[0] : null;
        currentFloorUnitplandId.value = floors.length > 0 ? floors[0] : null;
        if (selectedFloorId.value && Store.unitplanData[selectedFloorId.value]) {
          lowResData.value = cdn(Store.unitplanData[selectedFloorId.value].thumbnail);
        }
      } else {
        selectedFloorId.value = null;
        currentFloorUnitplandId.value = null;
        lowResData.value = cdn(current.thumbnail);
      }
    }
    getListOfUnitplanNames();

  });
} else {
  const keys = Object.keys(filteredUnitplanData.value);
  fetchImageURL(cdn(filteredUnitplanData.value[keys[0]].thumbnail)).then(() => {
    Object.values(filteredUnitplanData.value).map((item) => {
      return fetchImageURL(cdn(item.thumbnail));
    });
  });
  router.push({ query: { unitplan_id: filteredUnitplanData.value[keys[0]]._id, type: route.query.type }});
  // Set the first index
  selectedUnitPlan.value = filteredUnitplanData.value[route.query.unitplan_id];
  const current = filteredUnitplanData.value[route.query.unitplan_id];
  if (current) {
    if (current.unit_type === 'villa') {
      const floors = current.floor_unitplans || [];
      selectedFloorId.value = floors.length > 0 ? floors[0] : null;
      currentFloorUnitplandId.value = floors.length > 0 ? floors[0] : null;
      if (selectedFloorId.value && Store.unitplanData[selectedFloorId.value]) {
        lowResData.value = cdn(Store.unitplanData[selectedFloorId.value].thumbnail);
      }
    } else {
      selectedFloorId.value = null;
      currentFloorUnitplandId.value = null;
      lowResData.value = cdn(current.thumbnail);
    }
  }
  getListOfUnitplanNames();

}

if (Object.values(Store.allUnitCardData).length===0) {
  Store.getCountForUnits(route.params.projectId, route.params.organizationId);
}

watch(() => route.query.unitplan_id, () => {
  highResdata.value=false;
  if (Store.listOfVrTourData && filteredUnitplanData.value){
    if (Object.keys(itemsRefs.value).length > 0 && route.query.unitplan_id){
      if (!Store.isMobile){
        itemsRefs.value[route.query.unitplan_id].$el.scrollIntoView({  block: 'center', behavior: 'smooth' });
      } else {
        itemsRefs.value[route.query.unitplan_id].scrollIntoView({  inline: 'center', behavior: 'smooth' });
        showUnitplanCard.value = true;
      }
    }
  }
});

watch(() => route.query.unitplan_id, (newId) => {

  highResdata.value = false;
  if (filteredUnitplanData.value[newId]) {
    selectedUnitPlan.value = filteredUnitplanData.value[newId];
    if (filteredUnitplanData.value[newId].unit_type === 'villa') {
      const floors = filteredUnitplanData.value[newId].floor_unitplans || [];
      selectedFloorId.value = floors.length > 0 ? floors[0] : null;
      currentFloorUnitplandId.value = floors.length > 0 ? floors[0] : null;
      if (selectedFloorId.value && Store.unitplanData[selectedFloorId.value]) {
        lowResData.value = cdn(Store.unitplanData[selectedFloorId.value].thumbnail);
      }
    } else {
      selectedFloorId.value = null;
      currentFloorUnitplandId.value = null;
      lowResData.value = cdn(filteredUnitplanData.value[newId].thumbnail);
    }
  }
});

watch(selectedFloorId.value, (newFloorId) => {
  if (newFloorId && Store.unitplanData[newFloorId]) {
    lowResData.value = cdn(Store.unitplanData[newFloorId].thumbnail);
    highResdata.value = false;
  }
});

watch(() => filteredUnitplanData.value, (newVal) => {
  if (newVal && route.query.unitplan_id && newVal[route.query.unitplan_id]) {
    selectedUnitPlan.value = newVal[route.query.unitplan_id];

    // Initialize currentFloorUnitplandId for villa units
    if (newVal[route.query.unitplan_id]?.unit_type === 'villa' && newVal[route.query.unitplan_id]?.floor_unitplans?.length > 0) {
      currentFloorUnitplandId.value = newVal[route.query.unitplan_id].floor_unitplans[0];
    }
  }
}, { immediate: true, deep: true });

watch(itemsRefs, () => {
  if (Object.keys(itemsRefs.value).length > 0 && route.query.unitplan_id){
    if (!Store.isMobile){
      if (itemsRefs.value[route.query.unitplan_id]){
        itemsRefs.value[route.query.unitplan_id].$el.scrollIntoView({  block: 'center', behavior: 'smooth' });
      }
    } else {
      itemsRefs.value[route.query.unitplan_id].scrollIntoView({  inline: 'center', behavior: 'smooth' });
      showUnitplanCard.value = true;
    }
  }
}, { deep: true });

window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();

if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}

function moveToLocation (id, type, optionId){
  // Check if there's a last visited route for this option
  const lastVisitedRoute = Store.getLastVisitedRoute(optionId);

  if (lastVisitedRoute && lastVisitedRoute.name) {
    router.push(lastVisitedRoute);
    return;
  }

  // Default navigation logic
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer', query: {...route.query}});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}

const fullScreenChangeHandler = () => {
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    Store.isFullScreen = false;
  }
};

// Function gotoFavourites (){
//   IsUntiPlanFavoritesOpen.value = !isUntiPlanFavoritesOpen.value;
// }

// function handleUnitPlanSelection (item) {
//   showVrTour.value = false;
//   highResdata.value = false;
//   loaderProgress.value=0;
//   setTimeout(() => {
//     router.replace({ query: { unitplan_id: item._id }});
//     showVrTour.value = true;
//     selectedUnitPlan.value = Store.unitplanData[item._id];
//   }, 100);
// }

function handleUnitPlanSelection (item) {
  router.replace({ query: { unitplan_id: item._id }});
  highResdata.value = false;
}
/* setTimeout(() => {
  showSlider.value=true;
}, 1000);
 */
const handleBackNavigation = () => {
  const previousUnitplanRoute = Store.getLastVisitedRoute('previous_unitplan');
  router.push(previousUnitplanRoute);
};

function enterVR (){
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  if (filteredUnitplanData.value && Store.listOfVrTourData){
    showInterior.value = filteredUnitplanData.value[route.query.unitplan_id].tour_id? filteredUnitplanData.value[route.query.unitplan_id].tour_id:filteredUnitplanData.value[Store.unitplanData[route.query.unitplan_id].unitplan_id].tour_id;
    timespent.value = difference;
    startTime.value=new Date();
    Store.selectedTabOption = '360 tour';
  }
}

function buildAnalyticsFields () {
  let fields = {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    type: filteredUnitplanData.value[route.query.unitplan_id]?.unit_type,
    bedroom_number: formatBedrooms(filteredUnitplanData.value[route.query.unitplan_id]?.bedrooms),
    unit_size: filteredUnitplanData.value[route.query.unitplan_id]?.measurement,
    measurement_type: filteredUnitplanData.value[route.query.unitplan_id]?.measurement_type,
  };

  if (Object.values(Store.projectCardData).length > 0 || Object.values(watchedProjectCardData.value).length > 0) {
    fields = {
      ...fields,
      project_name: Store.projectCardData?.[route.params.projectId]?.name ?? watchedProjectCardData.value?.name,
      project_city: Store.projectCardData?.[route.params.projectId]?.city ?? watchedProjectCardData.value?.city,
    };
  }

  const price =  Object.values(Store.unitData).find((item) => item.unitplan_id === route.query.unitplan_id)?.price;
  const currency =  Object.values(Store.unitData).find((item) => item.unitplan_id === route.query.unitplan_id)?.currency;
  const status =  Object.values(Store.unitData).find((item) => item.unitplan_id === route.query.unitplan_id)?.status;

  if (price) {
    fields.unit_price = price;
  }
  if (currency) {
    fields.currency_type = currency;
  }
  if (status) {
    fields.availability_tag = status;
  }

  if (Store.buildingData[route.query.building_id]) {
    fields.project_type = Store.buildingData[route.query.building_id]?.category;
  }

  return fields;
}

function setCurrentActive (tab, imageId){
  currentImageId.value = imageId || null;
  activeNavigatorTab.value = tab;
  const keys = Object.keys(filteredUnitplanData.value);

  if (!isRealValue(route.query)) {
    router.push({ name: "unitplansviewer", query: {  unitplan_id: filteredUnitplanData.value[keys[0]]._id, type: tab } });
  } else {
    router.push({ name: "unitplansviewer", query: { ...route.query, type: tab } });
  }

  let  fields = buildAnalyticsFields();

  if (tab==='interior') {
    enterVR();
    fields={
      ...fields,
      tour_id: filteredUnitplanData.value[route.query.unitplan_id].tour_id? filteredUnitplanData.value[route.query.unitplan_id]?.tour_id:filteredUnitplanData.value[route.query.unitplan_id]?.tour_id,
      tour_name: filteredUnitplanData.value[route.query.unitplan_id].name? filteredUnitplanData.value[route.query.unitplan_id]?.name:filteredUnitplanData.value[route.query.unitplan_id]?.name,
    };
    Googleanalytics("tour_clicked", fields);
  } else if (tab==='exterior'){
    Googleanalytics("exterior_clicked", fields );
    if (filteredUnitplanData.value && filteredUnitplanData.value[route.query.unitplan_id]?.exterior_type === 'scene'){
      if (!Store.rotatableFrames[route.params.projectId]){
        Store.getRotatableFrames(route.params.projectId, route.params.organizationId, 'rotatable_image_frame', filteredUnitplanData.value[route.query.unitplan_id].scene_id).then((res) => {
          scene_data.value = res;
          showScene.value = true;
        });
      } else {
        scene_data.value = Store.rotatableFrames[route.params.projectId];
        showScene.value = true;
      }
    } else {
      if (filteredList.value.length===0 && Store.galleryList){
        filteredUnitplanData.value[route.query.unitplan_id].gallery_id.forEach((item) => {
          filteredList.value.push(Store.galleryList[item]);
        });
        activeTab.value=route.query.current_gallery;
      }
      // ShowGallery.value = true;
      selectedMediaIndex.value = 0 + 1; // Initial
    }
  } else {
    // ShowGallery.value = false;
    fields={
      ...fields,
      unitplan_id: route.query.unitplan_id,
      unitplan_name: filteredUnitplanData.value[route.query.unitplan_id]?.name,
    };
    Googleanalytics("unitplan_clicked", fields );
    showScene.value = false;
  }
}

watch(() => [filteredUnitplanData.value, Store.listOfVrTourData], ([newunitplanData, newvrtourData], [oldunitplanData, oldvrtourData]) => {
/*   if (newunitplanData){
    typeArray.value = Object.values(Store.unitplanData)
      .filter((unit) => unit.unit_type !== "villa_floor")
      .map((unit) => unit.name);
  } */
  if (newunitplanData !== oldunitplanData){
    if (filteredUnitplanData.value[route.query.unitplan_id]?.exterior_type === 'scene'){
      if (!Store.rotatableFrames[route.params.projectId]){
        Store.getRotatableFrames(route.params.projectId, route.params.organizationId, 'rotatable_image_frame', filteredUnitplanData.value[route.query.unitplan_id].scene_id).then((res) => {
          scene_data.value = res;
          showScene.value = true;
        });
      } else {
        scene_data.value = Store.rotatableFrames[route.params.projectId];
        showScene.value = true;
      }
    }
  } else if (newvrtourData !== oldvrtourData && route.query.unitplan_id && filteredUnitplanData.value[route.query.unitplan_id]?.tour_id){
    enterVR();
  }

});

const handleTypeChange = (type) => {
  const matchingUnit = Object.values(filteredUnitplanData.value).find((unit) => unit.name === type);
  router.push({name: "unitplansviewer", query: { unitplan_id: matchingUnit._id, type: route.query.type}});
  selectedTypeId.value = matchingUnit._id;
};
// Const handleFav = (item) => {
//   Store.updateAddRemoveUnitPlanFavorites(item);
// };

const callGoogleanalytics = () => {
  Googleanalytics("experience_start", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.isExperienceStartEventSent = true;
};

/* Hooks */
onMounted(() => {
  // Inital
  document.addEventListener('fullscreenchange', fullScreenChangeHandler);
  document.addEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('MSFullscreenChange', fullScreenChangeHandler);

  watch(
    () => Store.projectCardData[route.params.projectId],
    (newProjectData) => {
      if (newProjectData && Object.values(newProjectData).length > 0) {
        watchedProjectCardData.value = newProjectData;
      }
    }, { deep: true, immediate: true },
  );

  watch(
    () => [Store.organizationDetails, Store.projectCardData],
    ([newOrgDetails, newProjectCardData]) => {
      if (
        newOrgDetails && Object.keys(newOrgDetails).length > 0 &&
        newProjectCardData && Object.keys(newProjectCardData).length > 0
      ) {
        if (!Store.isExperienceStartEventSent && !Store.isNavigatedFromMasterScene) {
          callGoogleanalytics();
        }
      }
    },
    { immediate: false, deep: true },
  );
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('MSFullscreenChange', fullScreenChangeHandler);
  Store.hideLogo=false;
});

watch(() => Store.unitplanData[route.query.unitplan_id], (newVal) => {
  if (newVal && newVal.thumbnail) {
    if (newVal.unit_type === 'villa' && selectedFloorId.value && Store.unitplanData[selectedFloorId.value]) {
      lowResData.value = cdn(Store.unitplanData[selectedFloorId.value].thumbnail);
    } else {
      lowResData.value = cdn(newVal.thumbnail);
    }
  }
}, { immediate: true, deep: true });

watch(() => route.query.unitplan_id, () => {
  highResdata.value = false;
});
function findParentId (imageId, data) {
  const result = data.map((id) => Store.unitplanData[id] || `No data for ID: ${id}`);
  const groupedImageIds = {};

  result.forEach((unitData) => {
    const parentId = unitData._id;
    groupedImageIds[parentId] = [];
    if (unitData.hotspots){
      Object.values(unitData.hotspots).forEach((hotspot) => {
        if (hotspot.image_id) {
          groupedImageIds[parentId].push(hotspot.image_id);
        } else if (hotspot.label_id){
          groupedImageIds[parentId].push(hotspot.label_id);
        }
      });
    }
  });

  for (const parentId in groupedImageIds) {
    if (groupedImageIds[parentId].includes(imageId)) {
      currentFloorUnitplandId.value = parentId;
      break;
    }
  }
}
watch(() => currentImageId.value, (newVal) => {
  currentImageId.value = newVal;
  const floorUnitPlans = filteredUnitplanData.value[route.query.unitplan_id]?.floor_unitplans;
  if (floorUnitPlans && floorUnitPlans.length > 0) {
    findParentId(newVal, floorUnitPlans);
    defaultIndex.value = floorUnitPlans.indexOf(currentFloorUnitplandId.value);
    if (defaultIndex.value !== -1) {
      document.getElementById('overflowSliderRef')?.swiper.slideTo(defaultIndex.value, 0);
    }
  }
});

watch(() => currentFloorUnitplandId.value, (newVal) => {
  currentFloorUnitplandId.value = newVal;
});

const floorDataForButtonGroup = computed(() => {
  const unitplanId = route.query.unitplan_id;
  const filtered = { ...filteredUnitplanData.value }; // shallow copy

  const currentUnitplan = filtered[unitplanId];

  if (
    activeNavigatorTab.value === 'unitplan' &&
    currentUnitplan &&
    currentUnitplan.unit_type === 'villa'
  ) {
    const floorIds = currentUnitplan.floor_unitplans || [];

    floorIds.forEach((id) => {
      if (!filtered[id] && Store.unitplanData[id]) {
        filtered[id] = Store.unitplanData[id];
      }
    });
  }

  return filtered;
});

const slideView = computed(() => {
  const floorUnitPlans = filteredUnitplanData.value[route.query.unitplan_id]?.floor_unitplans || [];
  return floorUnitPlans.length >= 3 ? 3 : floorUnitPlans.length;
});

function setCurrentFloor (id) {
  selectedFloorId.value = id;
  if (Store.unitplanData[id]) {
    lowResData.value = cdn(Store.unitplanData[id].thumbnail);
    highResdata.value = false;
  }
}

function closeMap () {
  showMiniMap.value = false;
}

const handlePrev = () => {
  const currentIndex = filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans.indexOf(currentFloorUnitplandId.value);
  if (currentIndex > 0) {
    const prevIndex = currentIndex - 1;
    currentFloorUnitplandId.value = filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans[prevIndex];
  }
  const swiperContainer = document.getElementById('overflowSliderRef')?.swiper;
  swiperContainer?.slidePrev();
};

const handleNext = () => {
  const currentIndex = filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans.indexOf(currentFloorUnitplandId.value);
  if (currentIndex < filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans.length - 1) {
    const nextIndex = currentIndex + 1;
    currentFloorUnitplandId.value = filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans[nextIndex];
  }
  const swiperContainer = document.getElementById('overflowSliderRef')?.swiper;
  swiperContainer?.slideNext();
};

const handleSelection = (val) => {
  currentFloorUnitplandId.value = val;
};

const handleSwiperSlideChange = (val) => {
  const currentIndex = val.detail[0].realIndex;
  if (filteredUnitplanData.value[route.query.unitplan_id]?.floor_unitplans) {
    currentFloorUnitplandId.value = filteredUnitplanData.value[route.query.unitplan_id].floor_unitplans[currentIndex];
  }
};

function handleLowResLoaded () {
  // This function can be used for any low res loaded handling if needed
}

function updatedImageId (id) {
  currentImageId.value = id;
}

watch(() => [Store.unitplanData[route.query.unitplan_id], currentFloorUnitplandId.value], ([newVal, activeTab]) => {
  if (isRealValue(newVal)) {
    const unitplanArray = newVal.floor_unitplans;
    if (unitplanArray){
      let currentTab = activeTab;
      if (!currentTab) {
        currentTab = unitplanArray[0];
      } else {
        currentTab = currentFloorUnitplandId.value;
      }
      if (currentFloorUnitplandId.value !== currentTab) {
        currentFloorUnitplandId.value = currentTab;
      }
      const unitArray = unitplanArray.map((key) => {
        const unitplandata = Store.unitplanData[key];
        return {
          name: unitplandata.name,
          measurement: unitplandata.measurement || 0,
          measurement_type: unitplandata.measurement_type,
        };
      });
      unitPlanArrayData.value = unitArray;
    }
  }
}, { immediate: true });
</script>

<template>
  <CircularLoader v-if="!filteredUnitplanData || Object.keys(filteredUnitplanData).length === 0" />
  <div :style="{ fontFamily: Store.loadedFont }">
    <NavBar
      :is-root="isRoot"
      @handle-navigation="handleBackNavigation"
    />

    <div
      v-if="!Store.isMobile && !Store.isLandscape"
      class="absolute pb-8 !bottom-0 left-8 transform  z-[4] flex flex-col gap-5"
    >
      <BottomNavigation
        v-if="filteredUnitplanData && route.query.unitplan_id && filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].tour_id"
        :navigator="{
          'unitplan':true,
          'interior': filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].tour_id !== null && filteredUnitplanData[route.query.unitplan_id].tour_id !== undefined ,
          'exterior': filteredUnitplanData && filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].exterior_type ? true : false
        }"
        :activeTab="activeNavigatorTab"
        @set-current-active="setCurrentActive"
      />
    </div>

    <TabsView
      v-if="(Store.isMobile || Store.isLandscape) && filteredUnitplanData && route.query.unitplan_id && filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].tour_id"
      :navigator="{
        'unitplan':true,
        'interior': filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].tour_id !== null && filteredUnitplanData[route.query.unitplan_id].tour_id !== undefined ,
        'exterior': filteredUnitplanData && filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].exterior_type ? true : false
      }"
      class="bg-black w-full absolute z-[2]"
      :class="Store.isLandscape? 'top-[3rem] ' : 'top-[3.7rem] '"
      :default-id="activeNavigatorTab"
      @handle-selection="setCurrentActive"
    />

    <!-- Floor Plan Card -->
    <div
      v-if="filteredUnitplanData && route.query.unitplan_id && filteredUnitplanData[route.query.unitplan_id]"
      class="fixed "
      :class="[
        Store.isMobile
          ? 'bottom-0 !p-0 !pb-2 w-full'
          : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
            ? 'right-24 bottom-6'
            : Store.isLandscape
              ? '!right-8 bottom-6'
              : 'md:right-8 md:bottom-auto top-auto bottom-16 md:top-24', Store.isMobile ? (route.query['type'] === 'unitplan' ? 'z-[4]' : showMiniMap ? 'z-[0]' : 'z-[4]') : 'z-[4]'
      ]"
    >
      <!-- location, status, price, maxPrice, currency, unitId, cta_link not included-->
      <NewFloorPlanCard
        :class="Store.isMobile ? 'slide-animation' : 'slide'"
        :unitplanCta="true"
        :bedroomNumber="Store.unitplanData && Store.unitplanData[route.query.unitplan_id]?.bedrooms"
        :unitType="Store.unitplanData && Store.unitplanData[route.query.unitplan_id]?.unit_type"
        :unitPlans="unitPlanArrayData"
        :unitName="Store.unitplanData[route.query.unitplan_id]?.name"
        :isMobile="Store.isMobile"
        :hideClose="false"
        :styleType="Store.unitplanData && Store.unitplanData[route.query.unitplan_id]?.style ? Store.unitplanData[route.query.unitplan_id]?.style : null"
        :measurement="Store.unitplanData[route.query.unitplan_id]?.measurement"
        :measurementType="Store.unitplanData[route.query.unitplan_id]?.measurement_type"
        :balcony="(!Store.unitplanData[route.query.unitplan_id]?.unit_type || Store.unitplanData[route.query.unitplan_id]?.unit_type === 'flat' && Store.unitplanData[route.query.unitplan_id].balcony_measurement !== 0) && Store.unitplanData[route.query.unitplan_id].balcony_measurement"
        :balconyType="!Store.unitplanData[route.query.unitplan_id]?.unit_type || Store.unitplanData[route.query.unitplan_id]?.unit_type === 'flat' && Store.unitplanData[route.query.unitplan_id].balcony_measurement_type !== 0 && Store.unitplanData[route.query.unitplan_id].balcony_measurement_type"
        :suiteArea="Store.unitplanData[route.query.unitplan_id]?.suite_area"
        :suiteAreaType="Store.unitplanData[route.query.unitplan_id]?.suite_area_type"
        :totalArea="Store.unitplanData[route.query.unitplan_id].measurement"
        :favUnits="Store.favoritesData"
        :hideStatus="Store.hideStatus"
        :is_commercial="Store.unitplanData[route.query.unitplan_id]?.is_commercial !== undefined ? Store.unitplanData[route.query.unitplan_id]?.is_commercial : false"
        class="shadow-none"
        @handle-cta="handleCta"
        @closemodal="isVisible = !isVisible"
      />
    </div>

    <div class="flex flex-col w-inherit">
      <div class="flex-shrink-0  z-[2]  sm:z-auto ">
        <NewAleSideBar
          :sidebarList="Store.sidebarOptions[route.params.projectId]"
          @select-option="moveToLocation"
        />
      </div>
    </div>

    <div class="flex  w-full h-[inherit] relative">
      <!-- unitplan -->
      <div
        v-if="filteredUnitplanData && route.query.unitplan_id && selectedTabOption === 'unitplan' && activeNavigatorTab==='unitplan'"
        class=" w-full h-full flex-auto"
      >
        <div
          class="absolute sm:bottom-[29%] sm:right-8  bottom-2 right-[50%] z-[1] justify-center items-center w-full"
        >
          <ButtonGroup
            v-if="activeNavigatorTab==='unitplan' && filteredUnitplanData[route.query.unitplan_id]?filteredUnitplanData[route.query.unitplan_id].unit_type==='villa':false"
            :floorNames="filteredUnitplanData[route.query.unitplan_id].floor_unitplans"
            :floorData="floorDataForButtonGroup"
            :activeTab="currentFloorUnitplandId || filteredUnitplanData[route.query.unitplan_id].floor_unitplans[0]"
            :class="Store.isMobile ?'top-[8rem] sm:top-auto' :''"
            @set-current-floor="setCurrentFloor"
          />
        </div>

        <div
          style="background-image: url('/assets/bg_unitplan.png')"
          class="h-screen bg-cover bg-center w-full relative"
        >
          <!-- Picture -->
          <div class="w-full h-[80%] md:h-[92.5%] relative flex justify-center items-center mobile-screen">
            <div
              class="flex items-center justify-center relative"
              style="width: inherit; height: inherit"
            >
              <div class="w-[85%] h-[80%] relative flex justify-center items-center">
                <HotSpotViewer
                  v-if="filteredUnitplanData[route.query.unitplan_id]?.unit_type !== 'villa'"
                  :highResUrl="Store.unitplanData[route.query.unitplan_id]?.image_url"
                  :lowResUrl="Store.unitplanData[route.query.unitplan_id]?.thumbnail"
                  :hotSpots="Store.unitplanData[route.query.unitplan_id]?.hotspots"
                  :activeNavigatorTab="activeNavigatorTab"
                  :iszoomenabled="false"
                  @set-current-active="setCurrentActive"
                />
                <HotSpotViewer
                  v-if="selectedFloorId && Store.unitplanData[selectedFloorId].unit_type == 'villa_floor' && Store.unitplanData[route.query.unitplan_id].unit_type === 'villa'"
                  :highResUrl="Store.unitplanData[selectedFloorId]?.image_url"
                  :lowResUrl="Store.unitplanData[selectedFloorId]?.thumbnail"
                  :hotSpots="Store.unitplanData[selectedFloorId]?.hotspots"
                  :activeNavigatorTab="activeNavigatorTab"
                  :iszoomenabled="false"
                  @set-current-active="setCurrentActive"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="listOfUnitplanNames"
          class="absolute"
          :class="[ Store.isLandscape
                      ? Object.keys(Store.sidebarOptions || {}).length > 0
                        ? (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled
                          ? 'right-[6.5rem] bottom-44'
                          : 'right-36 bottom-28')
                        : (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled
                          ? 'right-10 bottom-44' // Sidebar is empty, CTA enabled
                          : 'right-[5.5rem] bottom-28' // Sidebar is empty, CTA not enabled
                        )
                      : '',
                    Store.isMobile
                      ? Object.keys(Store.sidebarOptions || {}).length > 0
                        ? (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled
                          ? 'bottom-[11.75rem] left-0 ml-4' // Sidebar has values, CTA enabled
                          : (Store.isIOS ? 'bottom-[10.25rem] left-0 ml-4' : 'bottom-[8.75rem] left-0 ml-4') // Sidebar has values, CTA disabled
                        )
                        : (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled
                          ? (Store.isIOS ? 'bottom-[8.75rem] left-1 ml-4' : 'bottom-[7rem] left-1 ml-4') // Sidebar empty, CTA enabled
                          : (Store.isIOS ? 'bottom-[5.75rem] left-0 ml-4' : 'bottom-[4.25rem] left-0 ml-4' ) // Sidebar empty, CTA disabled
                        )
                      : '',
                    !Store.isMobile && !Store.isLandscape?'bottom-8 right-4':''
          ]"
        >
          <DropDown
            placement="top"
            :list="listOfUnitplanNames"
            :defaultValue="filteredUnitplanData[route.query.unitplan_id] && filteredUnitplanData[route.query.unitplan_id].name"
            type="Array"
            svgPosition="right"
            :svg="dropDownSvg"
            :align-to-end="Store.isMobile ? false : true"
            @select-option="handleTypeChange"
          />
        </div>

        <!-- UnitPlanCards - MobileView -->
        <!-- <div
          v-if="Store.isMobile && filteredUnitplanData && route.query.unitplan_id"
          class="left-0 w-full fixed top-[7.5rem]"
        >
           :class="showUnitplanCard ? ( Store.unitplanData[route.query.unitplan_id].tour_id !== null && Store.unitplanData[route.query.unitplan_id].tour_id !== undefined ? 'bottom-[270px]' : 'bottom-[220px]' ) : 'top-[90px]'"
          <div
            class="transition-all duration-300 ease-linear flex justify-start items-center gap-5 w-full px-5 overflow-x-auto flex-nowrap invisibleScroll"
          >
            <div
              v-for="(item, index) in filteredUnitplanData"
              :id="filteredUnitplanData[route.query.unitplan_id]._id"
              :key="index"
              :ref="(el) => (itemsRefs[index] = el)"
              :class="(route.query.unitplan_id === item._id) ? 'text-primaryText bg-primary bg-opacity-40 text-xs font-medium shrink-0 px-6 py-3 rounded-[50px] backdrop-blur-[20px]' : 'text-secondaryText bg-secondary bg-opacity-40 text-xs font-medium shrink-0 px-6 py-3 rounded-[50px] backdrop-blur-[20px]'"
              @click="() =>{ router.push({ query: { unitplan_id: item._id }}); showUnitplanCard = true; }"
            >
              {{ item.name.replace(/[^a-zA-Z0-9]/g,' ') }}
            </div>
          </div>
        </div> -->
      </div>

      <div
        v-else
        class=" fixed w-full h-full flex-auto"
      >
        <TourView
          v-if="selectedUnitPlan && selectedUnitPlan.tour_id"
          :showInterior="selectedUnitPlan.tour_id"
          :listOfVrTourData="Store.listOfVrTourData[selectedUnitPlan.tour_id]"
          :currentImageId="currentImageId"
          @updated-image-id="updatedImageId"
          @current-image-id-emit="updatedImageId"
        />

        <!--unitplan preview-->
        <div
          v-if="activeNavigatorTab === 'interior'"
          class="md:w-[18vw] md:max-w-[20vw]"
          :class="[
            'absolute bottom-10 transform flex flex-col gap-5 w-full',
            Store.isMobile
              ? (showMiniMap
                ? 'z-[10] !h-[45%]'
                : 'mb-40 right-3'
              )
              : (showMiniMap || Store.isLandscape
                ? 'z-[1] mb-14 right-28 xl:right-8 !h-[30%] !w-[18vw]'
                : 'z-[1] mb-14 right-28 xl:right-8'
              ),
            !Store.isMobile && !showMiniMap ? '' : ''
          ]"
        >
          <!-- Minimap icon -->
          <div
            v-if="!showMiniMap"
            class=" w-9 h-9 md:w-10 md:h-10 bg-secondary absolute rounded-[2rem] flex !items-center !justify-center backdrop-blur-[3.125rem] bottom-0 bg-opacity-60"
            :class="[Store.isLandscape ? '-right-[1.5rem] -bottom-[75px] -mr-2': Store.isMobile ? '-right-[4px]' :'right-2' , Store.isMobile ? '-bottom-[7.6rem] ' : 'bottom-0 ']"
            @click="showMiniMap = !showMiniMap"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              class="w-5 h-5"
            >
              <path
                d="M9.5625 6.4375H9.56812"
                stroke="var(--secondaryText)"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9.5625 4.25C10.7463 4.25 11.75 5.25824 11.75 6.48144C11.75 7.72412 10.73 8.59619 9.78781 9.18919C9.71913 9.22906 9.6415 9.25 9.5625 9.25C9.4835 9.25 9.40587 9.22906 9.33719 9.18919C8.39675 8.59038 7.375 7.72844 7.375 6.48144C7.375 5.25824 8.37869 4.25 9.5625 4.25Z"
                stroke="var(--secondaryText)"
              />
              <path
                d="M2.0625 8C2.0625 5.20104 2.0625 3.80156 2.93202 2.93202C3.80156 2.0625 5.20104 2.0625 8 2.0625C10.7989 2.0625 12.1984 2.0625 13.068 2.93202C13.9375 3.80156 13.9375 5.20104 13.9375 8C13.9375 10.7989 13.9375 12.1984 13.068 13.068C12.1984 13.9375 10.7989 13.9375 8 13.9375C5.20104 13.9375 3.80156 13.9375 2.93202 13.068C2.0625 12.1984 2.0625 10.7989 2.0625 8Z"
                stroke="var(--secondaryText)"
              />
              <path
                d="M11.125 13.625L2.375 4.875"
                stroke="var(--secondaryText)"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M6.75 9.25L3 13"
                stroke="var(--secondaryText)"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div
            v-if="showMiniMap"
            class="flex border border-none rounded-xl bg-secondary py-4 px-4 relative h-full justify-center items-center"
            :class="Store.isMobile ? 'flex-col-reverse justify-around' : ''"
          >
            <div
              class="absolute top-2 right-2 cursor-pointer"
              @click="closeMap"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="19"
                viewBox="0 0 18 19"
                fill="none"
              >
                <path
                  d="M13.5 14.25L4.5 5.25"
                  stroke="var(--secondaryText)"
                  stroke-width="1.18125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M13.5 5.25L4.5 14.25"
                  stroke="var(--secondaryText)"
                  stroke-width="1.18125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div
              v-if="currentFloorUnitplandId"
              class="flex justify-center items-center border border-[#374151] z-40  rounded bg-secondary"
              :class="Store.isMobile ? 'flex-row h-[15%] w-[70%]' : 'flex-col h-[70%] w-[15%]'"
            >
              <button
                v-if="filteredUnitplanData[route.query.unitplan_id]?.unit_type === 'villa'"
                class="flex items-center justify-center"
                :class="[Store.isMobile ? 'w-[20%] h-full rotate-[-90deg]' : 'h-[20%] w-full']"
                :disabled="filteredUnitplanData[route.query.unitplan_id]?.floor_unitplans.indexOf(currentFloorUnitplandId) === 0"
                @click="handlePrev"
              >
                <svg
                  class="w-8 h-fit"
                  viewBox="0 0 16 16"
                  fill="white"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4 10.06L4.94 11L8 7.94667L11.06 11L12 10.06L8 6.06L4 10.06Z"
                    fill="#D8DBDF"
                  />
                </svg>
              </button>
              <OverflowSlider
                id="overflowSliderRef"
                :direction="Store.isMobile ? 'horizontal' : 'vertical'"
                :slides-per-view="slideView"
                :initial-slide="defaultIndex"
                :class="Store.isMobile ? 'w-[60%] h-full' : 'h-[60%] w-full'"
                @swiper-slide-change="handleSwiperSlideChange"
              >
                <template #options>
                  <swiper-slide
                    v-for="(item, index) in filteredUnitplanData[route.query.unitplan_id]?.floor_unitplans"
                    :key="item"
                    :class="[
                      (currentFloorUnitplandId === item ? 'bg-primary text-primaryText ' : 'text-secondaryText hover:text-secondaryText bg-secondary'),
                      'aspect-square bg-opacity-75 shadow-inner backdrop-blur-[7.50px] justify-center items-center inline-flex cursor-pointer !mb-0 border border-[#374151]'
                    ]"
                    class="text-primaryText"
                    @click="handleSelection(item)"
                  >
                    <p class="text-inherit  font-medium text-center uppercase lg:text-base text-[10px] ">
                      {{ index + 1 }}
                    </p>
                  </swiper-slide>
                </template>
              </OverflowSlider>
              <button
                v-if="filteredUnitplanData[route.query.unitplan_id]?.unit_type === 'villa'"
                class="flex items-center justify-center"
                :class="[Store.isMobile ? 'w-[20%] h-full rotate-[-90deg]' : 'h-[20%] w-full']"
                :disabled="filteredUnitplanData[route.query.unitplan_id]?.floor_unitplans.indexOf(currentFloorUnitplandId) === filteredUnitplanData[route.query.unitplan_id]?.floor_unitplans.length - 1"
                @click="handleNext"
              >
                <svg
                  class="w-8 h-fit"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 6.94L11.06 6L8 9.05333L4.94 6L4 6.94L8 10.94L12 6.94Z"
                    fill="#D8DBDF"
                  />
                </svg>
              </button>
            </div>
            <HotSpotViewer
              v-if="filteredUnitplanData[route.query.unitplan_id] ? filteredUnitplanData[route.query.unitplan_id].unit_type !=='villa' : false"
              :highResUrl="Store.unitplanData[route.query.unitplan_id].image_url"
              :lowResUrl="Store.unitplanData[route.query.unitplan_id].thumbnail"
              :hotSpots="Store.unitplanData[route.query.unitplan_id].hotspots"
              :activeNavigatorTab="activeNavigatorTab"
              :currentImageId="currentImageId"
              @set-current-active="setCurrentActive"
              @low-res-loaded="handleLowResLoaded"
              @current-image-id-emit="updatedImageId"
            />
            <HotSpotViewer
              v-if="currentFloorUnitplandId"
              :highResUrl="Store.unitplanData[currentFloorUnitplandId].image_url"
              :lowResUrl="Store.unitplanData[currentFloorUnitplandId].thumbnail"
              :hotSpots="Store.unitplanData[currentFloorUnitplandId].hotspots"
              :activeNavigatorTab="activeNavigatorTab"
              :currentImageId="currentImageId"
              @set-current-active="setCurrentActive"
              @low-res-loaded="handleLowResLoaded"
              @current-image-id-emit="updatedImageId"
            />
          </div>
        </div>

        <div
          v-if="selectedUnitPlan && showVrTour && !Store.isMobile"
          class="z-10 absolute"
          :class="Store.isLandscape ? (Store.projectCardData[route.params.projectId]?.projectSettings.ale?.is_cta_enabled ? 'right-10 bottom-20' : 'right-36 bottom-5') :'right-10 bottom-10'"
        >
          <DropDown
            :unitPlandId="route.query.unitplan_id"
            :align-to-end="true"
            placement="top"
            :list="Object.values(filteredUnitplanData).filter(element => element.tour_id)"
            :defaultValue="selectedUnitPlan"
            objectKey="name"
            type="Object"
            @select-option="handleUnitPlanSelection"
          />
        </div>
        // dropmenu for mobile
        <div
          v-if="Store.isMobile && selectedUnitPlan"
          class="left-0 w-full fixed top-[7.5rem]"
        >
          <!-- :class="showUnitplanCard ? ( Store.unitplanData[route.query.unitplan_id].tour_id !== null && Store.unitplanData[route.query.unitplan_id].tour_id !== undefined ? 'bottom-[270px]' : 'bottom-[220px]' ) : 'top-[90px]'" -->
          <div
            class="transition-all duration-300 ease-linear flex justify-start items-center gap-5 w-full px-5 overflow-x-auto flex-nowrap invisibleScroll"
          >
            <div
              v-for="(item, index) in filteredUnitplanData"
              :id="filteredUnitplanData[route.query.unitplan_id]._id"
              :key="index"
              :ref="(el) => (itemsRefs[index] = el)"
              :class="(route.query.unitplan_id === item._id) ? 'text-primaryText bg-primary bg-opacity-40 text-xs font-medium shrink-0 px-6 py-3 rounded-[50px] backdrop-blur-[20px]' : 'text-secondaryText bg-secondary bg-opacity-40 text-xs font-medium shrink-0 px-6 py-3 rounded-[50px] backdrop-blur-[20px]'"
              @click="() =>{ router.push({ query: { unitplan_id: item._id, type:route.query.type }}); showUnitplanCard = true; }"
            >
              {{ item.name.replace(/[^a-zA-Z0-9]/g,' ') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="hidden">
      <MatterportView :space-id="null" />
    </div> -->
    <!--  <UnitPlanFavoriteView
      v-if="isUntiPlanFavoritesOpen"
      :property="Store.unitPlanFavoritesData"
      @handle-close="() => isUntiPlanFavoritesOpen = false"
      @handle-click="(id) => { router.replace({ query: { unitplan_id: id }}); isUntiPlanFavoritesOpen = false; }" -->

    <!-- <UnitPlanCard
            :class="[showUnitplanCard ? 'bottom-[70px]' : '-bottom-[200px]','absolute left-0 transition-all duration-300 ease-linear']"
            :name="Store.unitplanData[route.query.unitplan_id].name"
            :thumbnail="Store.unitplanData[route.query.unitplan_id].thumbnail"
            :measurementType="Store.unitplanData[route.query.unitplan_id].measurement_type"
            :measurement="Store.unitplanData[route.query.unitplan_id].measurement"
            :minMeasurement="Store.unitplanData[route.query.unitplan_id].minMeasurement"
            :maxMeasurement="Store.unitplanData[route.query.unitplan_id].maxMeasurement"
            :bedrooms="Store.unitplanData[route.query.unitplan_id].bedrooms"
            :allowEnterVr=" Store.unitplanData[route.query.unitplan_id].tour_id !== null && Store.unitplanData[route.query.unitplan_id].tour_id !== undefined"
            :favoritesStatus="Object.keys(Store.unitPlanFavoritesData).some((key) => Store.unitplanData[route.query.unitplan_id].name.replace(/[^a-zA-Z0-9]/g,' ') === key)"
            @enter-v-r="() => selectedTabOption = '360 tour'"
            @handle-close="() => showUnitplanCard = false"
            @handle-favorite="() => Store.updateAddRemoveUnitPlanFavorites(Store.unitplanData[route.query.unitplan_id])"
          /> -->
    />
  </div>
</template>

<style scoped>

.customStyle{
  position: relative;
}

.plan-modal {
background-image: url('/assets/unitplanbg.jpg');
background-size: cover;
background-repeat: no-repeat;
}

.slide
{

  animation:slide 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;

}
@keyframes slide
{
  0%
  {
    right :-24em;
  }
  50%
  {
    right:3em;
  }
  100%
  {
    @apply md:right-16;
  }
}

@media screen and (max-width: 430px) {
.slide-animation
{
  animation:popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
}

@keyframes popup
{
  0%
  {
    bottom :-24em;
  }
  100%
  {
    @apply md:bottom-0 ;
  }
}

.invisibleScroll::-webkit-scrollbar {
width: 0px;
}

.invisibleScroll::-webkit-scrollbar-track {
background: transparent;
}

.invisibleScroll::-webkit-scrollbar-thumb {
background: transparent;

}

@media only screen and (max-width: 640px) {
  .mobile-screen {
    transform: translate(0px, 3rem);
    scale: 0.9;
  }
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  will-change: opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  transition: opacity 0.5s ease-in-out;
}

.image-container img[src],
.image-container img[data-src] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.image-container img {
  position: absolute;
  top: 0; left: 0; width: 100%; height: 100%;
  object-fit: contain;
}
.image-container-default {
  position: relative;
  height: 100%;
  width: 65%;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
}
#image-container img:hover {
      /* cursor: zoom-in; */
}

</style>
