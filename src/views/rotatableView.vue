<script setup>
import { defineProps, ref, defineEmits, watch} from 'vue';
import { creationToolStore } from '../store/index';
import RotatableOverlay from '../components/svgOverlay/rotatableOverlay.vue';
// import PopUpMessageBox from '../components/ALEComponents/PopUpMessageBox/PopUpMessageBox.vue';
// import { getCookie } from '../helpers/helper';
import {useRoute} from 'vue-router';
import router from '../router/index.js';
import InfoModal from '../components/ALEComponents/InfoModal/InfoModal.vue';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import CloudTransition from '../components/ALEComponents/CloudTransition/CloudTransition.vue';
// Import BackTopBar from '../components/ALEComponents/BackBar/BackTopBar.vue';
import FloorPlanCard from '../components/ALEComponents/FloorPlanCard/FloorPlanCard.vue';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';

const Store = creationToolStore();
const scene_data = ref({});
const showLoader = ref(true);
const route = useRoute();
const props = defineProps({'sceneId': {type: String, default: ""}, 'scenes': {
  type: Object,
  default () {
    return {};
  },
}, 'organizationId': {type: String, default: ""}, projectId: {type: String, default: undefined}});
const emit = defineEmits(['removeLoader']);
// var  checkAutoExit = ref(false);

Store.callbackFunctionMonitorChanges();
window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
if (!Store.organization_thumbnail) {
  Store.getOrganization(props.organizationId);
}
if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(props.organizationId, route.params.projectId);
}
Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
watch(props, (newprops) => {
  Store.activeOptionId = Store.SceneData[newprops.sceneId].sceneData.category;
});
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
// const closeFullscreenModal = ref(false);
// if (!Store.rotatableFrames[props.projectId]){
Store.getRotatableFrames(props.projectId, props.organizationId, 'rotatable_image_frame', props.sceneId).then((res) => {
  scene_data.value = res;
});
// } else {
//   scene_data.value = Store.rotatableFrames[props.projectId];
// }
// function setFullScreenCookie () {
//   if (!getCookie('fullscreen')) {
//     const expiryTime = new Date(Date.now() + (30 * 60000));
//     document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
//     closeFullscreenModal.value = true;
//   }
// }
// ADDED
// const onClickButton = (id) => {

//   if (id === 'fullscreen') {
//     setFullScreenCookie();
//     if (!document.fullscreenElement &&
//       !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
//       if (document.documentElement.requestFullscreen) {
//         document.documentElement.requestFullscreen();
//       } else if (document.documentElement.mozRequestFullScreen) {
//         document.documentElement.mozRequestFullScreen();
//       } else if (document.documentElement.webkitRequestFullscreen) {
//         document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
//       } else if (document.documentElement.msRequestFullscreen) {
//         document.documentElement.msRequestFullscreen();
//       }
//       checkAutoExit.value = false;
//       Store.isFullScreen = !Store.isFullScreen;
//     } else {
//       if (document.exitFullscreen) {
//         document.exitFullscreen();
//       } else if (document.mozCancelFullScreen) {
//         document.mozCancelFullScreen();
//       } else if (document.webkitExitFullscreen) {
//         document.webkitExitFullscreen();
//       } else if (document.msExitFullscreen) {
//         document.msExitFullscreen();
//       }
//       checkAutoExit.value = true;
//       Store.isFullScreen = !Store.isFullScreen;
//     }

//   }
// };

if (Object.keys(Store.projectCardData).length === 0){
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}
function removeLoader () {
  emit('removeLoader');
  showLoader.value=false;
  if (props.scenes[props.sceneId].sceneData.root){
    setTimeout(() => {
      if (!Store.showInfoModal.includes(props.scenes[props.sceneId].sceneData._id)) {
        Store.showInfoModal.push(props.scenes[props.sceneId].sceneData._id);
      }
    }, 2000);
  }
}
function moveToLocation (id, type, optionId){
  // Check if there's a last visited route for this option
  const lastVisitedRoute = Store.getLastVisitedRoute(optionId);

  if (lastVisitedRoute && lastVisitedRoute.name) {
    router.push(lastVisitedRoute);
    return;
  }

  // Default navigation logic
  if (type==='masterscene'){
    router.push({name: 'masterScene', params: {sceneId: id} });
  } else if (type==='earth'){
    router.push({name: 'globeScene'});
  } else if (type==='projectscene') {
    router.push({name: 'projectScene', params: {sceneId: id}});
  } else if (type === 'unitplan'){
    router.push({ name: 'unitplansviewer'});
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push( { name: 'galleryview'} );
  }
}
</script>
<template>
  <!-- <NavBar :is-root="Store.SceneData[route.params.sceneId].sceneData.root" /> -->

  <!-- <div class="md:flex justify-center pointer-events-auto hidden">
    <div class="top-10 absolute z-10 ">
      <PopUpMessageBox
        v-if="!closeFullscreenModal && !getCookie('fullscreen')"
        message="Enter to full screen mode for best experience"
        button="Enter"
        @close-x="setFullScreenCookie"
        @enter="onClickButton('fullscreen')"
      />
    </div>
  </div> -->
  <!-- <LoaderComponent
    v-if="showLoader"
    class="z-10"
    :logo="Store.projectCardData[route.params.projectId]?Store.projectCardData[route.params.projectId]['projectSettings']['general']['branding_logo']:false"
  /> -->
  <CircularLoader
    v-if="showLoader"
    class="z-8"
  />
  <InfoModal
    v-if="!Store.showInfoModal.includes(props.scenes[sceneId].sceneData._id) && props.scenes[sceneId].sceneData.root && !showLoader"
    :info-text="props.scenes[sceneId].sceneData.info_text"
    :info-icon="Store.logo === 'light' ? 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fclick_white.svg?alt=media' : 'https://storagecdn.propvr.tech/CreationtoolAssets%2Fsiteassets%2Fblack_click.svg?alt=media'"
  />
  <CloudTransition
    v-if="props.scenes[sceneId].sceneData.clouds &&!showLoader"
    class="z-[1]"
  />
  <!-- <div
    v-if="!showLoader && props.scenes[sceneId].sceneData"
    class="absolute top-0 z-[4] sm:hidden"
  >
    <BackTopBar
      :is-root="props.scenes[sceneId].sceneData.root"
      :scene-name="route.query.floor_id? 'Floor '+route.query.floor_id: Store.SceneData[route.params.sceneId].sceneData.name"
      :thumbnail="Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo"
      @go-back="goToPreviousScene"
    />
  </div> -->
  <div
    v-if="!showLoader && Store.svgVisibility.showSVG"
    class="absolute top-[50%] sm:z-[1]"
  >
    <NewAleSideBar
      :sidebarList="Store.sidebarOptions[route.params.projectId]"
      @select-option="moveToLocation"
    />
  </div>
  <div
    v-if="showUnitplanCard"
    class="fixed top-0 floordataa z-20 left-0 w-screen h-screen"
    clickaway="true"
    @click="closeModal"
  >
    <FloorPlanCard
      style="position: absolute;right: 1.5rem;top: 8rem;"
      :unitId="Store.allUnitCardData[showUnitplanCard]._id"
      :title="Store.allUnitCardData[showUnitplanCard].name"
      :status="Store.allUnitCardData[showUnitplanCard].status"
      :price="Store.allUnitCardData[showUnitplanCard].price"
      :measurement="Store.allUnitCardData[showUnitplanCard].measurement"
      :bedrooms="Store.allUnitCardData[showUnitplanCard].bedroom"
      :currency-title="Store.allUnitCardData[showUnitplanCard].currency"
      :is-show-buttons="true"
      button-view="floorplanview"
      :isMobile="Store.isMobile"
      :hideStatus="Store.hideStatus"
      @show-unitplan="gotoUnit"
    />
  </div>

  <RotatableOverlay
    ref="containerRef"
    :data="scene_data"
    style="height:100%;width:100%"
    bucket-u-r-l="propvr-in-31420.appspot.com"
    replace-u-r-l="storagecdn.propvr.ai"
    @remove-loader="removeLoader"
  />
</template>
