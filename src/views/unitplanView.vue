<script setup>
import { ref, defineProps, watch, onMounted, onUnmounted, computed } from 'vue';
import router from '../router';
import { creationToolStore } from '../store';
import { useRoute } from 'vue-router';
import FloorPlanCard from '../components/ALEComponents/FloorPlanCard/FloorPlanCard.vue';
import { cdn, formatBedrooms, getSplashCookie, Googleanalytics, isRealValue  } from '../helpers/helper';
import NewAleSideBar from '../components/ALEComponents/NewAleSideBar/NewAleSideBar.vue';
import BottomNavigation from '../components/ALEComponents/BottomNavigation/BottomNavigation.vue';
import ButtonGroup from '../components/ALEComponents/ButtonGroup/ButtonGroup.vue';
import { fetchImageURL } from '../helpers/API';
import TabsView from '../components/ALEComponents/TabsView/TabsView.vue';
import NavBar from '../components/ALEComponents/NavBar/NavBar.vue';
import NewFloorPlanCard from '../components/ALEComponents/NewFloorPlanCard/NewFloorPlanCard.vue';
import HotSpotViewer from '../components/ALEComponents/HotSpotViewer/HotSpotViewer.vue';
import TourView from '../components/ALEComponents/TourView/TourView.vue';
import unitPlanScene from '../components/ALEComponents/unitPlanScene/unitPlanScene.vue';
import CircularLoader from '../components/ALEComponents/CircularLoader/CircularLoader.vue';
import OverflowSlider from '../components/ALEComponents/OverflowSlider/OverflowSlider.vue';
import { availUnitOrgs } from '../config/masterdata';
import RegisterInterest from '../components/ALEComponents/NewFloorPlanCard/RegisterInterest.vue';
const route = useRoute();
const Store = creationToolStore();
const props = defineProps({ 'sceneId': { type: String, default: "" }, 'projectId': { type: String, default: "" }, 'organizationId': { type: String, default: "" } });
const scenes = ref(false), showUnitplanCard = ref(false), scene_data = ref({});
const toggleAddFavoriteStatus = ref(false), showbottomNav = ref(true);
const showSlider = ref(false);
const startTime = ref(new Date), timespent = ref(0), activeNavigatorTab = ref(route.query.type?route.query.type:"unitplan"), showScene = ref(false), removeLoader =ref(false), showGallery = ref(false), activeTab = ref(false), showInterior = ref(false);
const activeFloorTab = ref(route.query.current_floor);
const unitplanId = ref(route.params.unitplanId), unitId = ref(route.query.unitId);
const lowResLoaded = ref(false);
const unitPlanArrayData = ref([]);
const tabsView = ref(null), isVisible = ref(false);
Store.hideLogo = true;
const buildingId = ref(route.query.building_id);
const filteredList = ref([]), selectedMediaIndex = ref(null), currentImageId = ref(null), showMiniMap = ref(Store.isMobile ? false : true), defaultIndex = ref(0);
const showRegisterInterest = ref(false);
if (!Store.organization_thumbnail) {
  Store.getOrganization(props.organizationId);
}
if (!Store.sidebarOptions[route.params.projectId]) {
  Store.getOptions(props.organizationId, route.params.projectId);
}
if (Object.keys(Store.projectCardData).length === 0) {
  Store.getListofProjects(route.params.organizationId, route.params.projectId);
}
if (Object.keys(Store.allUnitCardData).length === 0) {
  Store.getCountForUnits( route.params.projectId, route.params.organizationId).then((unitData) => {
    unitplanId.value = route.params.unitplanId?route.params.unitplanId:Object.values(unitData).filter((item) => item.name === route.query.unit_name)[0].unitplan_id;
    unitId.value = route.params.unit_id?route.params.unit_id:Object.values(unitData).filter((item) => item.name === route.query.unit_name)[0]._id;

  });
} else {
  unitplanId.value = route.params.unitplanId?route.params.unitplanId:Object.values(Store.allUnitCardData).filter((item) => item.name === route.query.unit_name)[0].unitplan_id;
  unitId.value = route.params.unit_id?route.params.unit_id:Object.values(Store.allUnitCardData).filter((item) => item.name === route.query.unit_name)[0]._id;

}

if (!Store.listOfVrTourData) {
  Store.getListOfVRId(route.params.organizationId, route.params.projectId);
}
if (Store.unitplanData[unitplanId.value]) {
  if (Store.unitplanData[unitplanId.value].unit_type === 'villa') {
    Store.unitplanData[unitplanId.value].floor_unitplans.forEach((item) => {
      fetchImageURL(cdn(Store.unitplanData[item].thumbnail));
    });
  }
}
if (Object.values(Store.communityData).length === 0){
  Store.getCommunities(route.params.projectId, route.params.organizationId);
}
if (!Store.unitData){
  Store.getListofUnits(route.params.projectId, route.params.organizationId).then((unitData) => {
    unitplanId.value = route.params.unitplanId?route.params.unitplanId:Object.values(unitData).filter((item) => item.name === route.query.unit_name)[0].unitplan_id;
    unitId.value = route.params.unit_id?route.params.unit_id:Object.values(unitData).filter((item) => item.name === route.query.unit_name)[0]._id;
  });
} else {
  unitplanId.value = route.params.unitplanId?route.params.unitplanId:Object.values(Store.allUnitCardData).filter((item) => item.name === route.query.unit_name)[0].unitplan_id;
  unitId.value = route.params.unit_id?route.params.unit_id:Object.values(Store.allUnitCardData).filter((item) => item.name === route.query.unit_name)[0]._id;
}

if (Object.values(Store.unitplanData).length===0) {
  Store.getListOfUnitplan(props.projectId, route.params.organizationId);
}
Store.updateSceneType(route.fullPath);
Store.getTranslation(route.params.organizationId);
watch(Store.favoritesData, () => {
  if (Object.keys(Store.allUnitCardData).length > 0) {
    if (Store.allUnitCardData[unitId.value]) {
      if (Object.keys(Store.favoritesData).length > 0) {
        if (Store.favoritesData[Store.allUnitCardData[unitId.value].name] === undefined || Store.favoritesData[Store.allUnitCardData[unitId.value].name] === null) {
          toggleAddFavoriteStatus.value = false;
        } else {
          toggleAddFavoriteStatus.value = true;
        }
      } else {
        toggleAddFavoriteStatus.value = false;
      }
    }
  }
});
const slideView = computed(() => {
  const floorUnitPlans =
    Store.unitplanData[unitplanId.value]?.floor_unitplans || [];
  return floorUnitPlans.length >= 3 ? 3 : floorUnitPlans.length;
});
const currentFloorUnitplandId = ref(Store.unitplanData[unitplanId.value]?.floor_unitplans ? Store.unitplanData[unitplanId.value]?.floor_unitplans[0] : '');

const handleCta = (type) => {

  const ctaLink = Store.allUnitCardData[unitId.value]?.cta_link;
  if (type==='custom' && ctaLink ){
    const currentEncodedUrl = encodeURIComponent(window.location.href);
    const url = new URL(ctaLink);
    url.searchParams.set("redirect", currentEncodedUrl);
    window.open(url.toString(), "_blank");
  } else if (type==='event_emit'){
    const newObject = {
      actiontype: "ctaClicked",
      unit: Store.allUnitCardData[unitId.value],
    };
    window.parent.postMessage(JSON.stringify(newObject), '*');
  } else if (type==='default'){
    showRegisterInterest.value = true;
  }
};

function enterVR () {
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  if (Store.unitData && Store.listOfVrTourData ) {

    showInterior.value = Store.unitData[unitId.value].tour_id ? Store.unitData[unitId.value].tour_id : Store.unitplanData[Store.unitData[unitId.value].unitplan_id]?.tour_id;
    timespent.value = difference;
    startTime.value = new Date();
    Store.selectedTabOption = '360 tour';
  }
}
watch(() => [Store.unitData, Store.unitplanData, Store.listOfVrTourData], ([newunitData, newunitplanData,  newvrtourData], [oldunitplanData, oldunitData, oldvrtourData]) => {

  if (newunitplanData && unitplanId.value && newunitplanData !== oldunitplanData) {
    if (newunitplanData[unitplanId.value].unit_type === 'villa') {
      currentFloorUnitplandId.value = newunitplanData[unitplanId.value].floor_unitplans[0];
    }
    if (Store.unitplanData[unitplanId.value].exterior_type === 'scene') {
      Store.getRotatableFrames(route.params.projectId, props.organizationId, 'rotatable_image_frame', Store.unitplanData[unitplanId.value].scene_id).then((res) => {
        scene_data.value = res;
        showScene.value = true;
      });
    }
  } else if (newunitData !== oldunitData || newvrtourData !== oldvrtourData) {
    if (route.query.type==='interior'){
      enterVR();
    }
  }
});
const splashCookie = getSplashCookie();
splashCookie[props.projectId] = true;
if (Object.values(Store.SceneData).length === 0) {
  Store.getProjectScenes(route.params.organizationId, props.projectId).then((res) => {
    scenes.value = res;
    Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
    if (scenes.value && !scenes.value[route.params.sceneId].sceneData.root) {
      document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
    }
  });
} else {
  scenes.value = Store.SceneData;
  Store.activeOptionId = Store.SceneData[route.params.sceneId].sceneData.category;
  if (scenes.value && !scenes.value[route.params.sceneId].sceneData.root) {
    document.cookie = `splashLoader=${JSON.stringify(splashCookie)}; path=/;`;
  }
}
function moveToLocation (id, type, optionId) {
  // Check if there's a last visited route for this option
  const lastVisitedRoute = Store.getLastVisitedRoute(optionId);

  if (lastVisitedRoute && lastVisitedRoute.name) {
    router.push(lastVisitedRoute);
    return;
  }

  // Default navigation logic
  if (type === 'masterscene') {
    router.push({ name: 'masterScene', params: { sceneId: id } });
  } else if (type === 'earth') {
    router.push({ name: 'globeScene' });
  } else if (type === 'projectscene') {
    router.push({ name: 'projectScene', params: { sceneId: id } });
  } else if (type === 'unitplan') {
    router.push({ name: 'unitplansviewer' });
  } else if (type === 'amenity'){
    router.push({ name: 'amenity'});
  } else if (type==='map') {
    router.push({name: 'map'});
  } else {
    router.push({ name: 'galleryview' });
  }
}

function gotoUnit () {
  router.push({ name: "unit.Child", params: { unitplanId: Store.allUnitCardData[showUnitplanCard.value].unitplan_id }, query: { ...route.query, unit_id: showUnitplanCard.value, unit_name: Store.allUnitCardData[showUnitplanCard.value].name } });
  showUnitplanCard.value = false;
}

function closeModal (e) {
  if (e.target.getAttribute("clickaway") === "true") {
    showUnitplanCard.value = false;
  }
}

function closeMap () {
  showMiniMap.value = false;
}

setTimeout(() => {
  showSlider.value = true;
}, 1000);

const callGoogleanalytics = () => {
  Googleanalytics("experience_start", {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
  Store.isExperienceStartEventSent = true;
};

onMounted(() => {
  if (Store.isMobile) {
    tabsView.value.setActiveButton(route.query.type);
  }
  // Inital
  if (Object.keys(Store.allUnitCardData).length > 0) {
    if (Store.allUnitCardData[unitId.value]) {
      if (Object.keys(Store.favoritesData).length > 0) {
        if (Store.favoritesData[Store.allUnitCardData[unitId.value].name] === undefined || Store.favoritesData[Store.allUnitCardData[unitId.value].name] === null) {
          toggleAddFavoriteStatus.value = false;
        } else {
          toggleAddFavoriteStatus.value = true;
        }
      } else {
        toggleAddFavoriteStatus.value = false;
      }
    }
  }

  // Store.updateSceneType(route.fullPath);

  // Watch for changes in organizationDetails and projectCardData
  watch(
    () => [Store.organizationDetails, Store.projectCardData],
    ([newOrgDetails, newProjectCardData]) => {
      if (
        newOrgDetails && Object.keys(newOrgDetails).length > 0 &&
        newProjectCardData && Object.keys(newProjectCardData).length > 0
      ) {
        if (!Store.isExperienceStartEventSent && !Store.isNavigatedFromMasterScene) {
          callGoogleanalytics();
        }
      }
    },
    { immediate: false, deep: true },
  );
});

function buildAnalyticsFields () {
  const fields =   {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
    project_city: Store.projectCardData?.[route.params.projectId]?.city,
    project_type: Store.buildingData[route.query.building_id]?.category,
    bedroom_number: formatBedrooms(Store.allUnitCardData[unitId.value]?.bedrooms),
    unit_size: Store.allUnitCardData[unitId.value]?.measurement,
    measurement_type: Store.allUnitCardData[unitId.value]?.measurement_type,
    unit_price: Store.allUnitCardData[unitId.value]?.price,
    currency_type: Store.allUnitCardData[unitId.value]?.currency,
    availability_tag: Store.allUnitCardData[unitId.value]?.status,
  };

  return fields;
}

function setCurrentFloor (id) {
  currentFloorUnitplandId.value = id;
  setTimeout(() => {
    currentFloorUnitplandId.value = id;
  }, 100);
  const fields =  buildAnalyticsFields();
  Googleanalytics('unitplan_floor_switch', fields);
}

Store.Navigator.Unitplan = true;
if ((Store.unitData && Store.unitData[unitId.value].tour_id) || (Store.unitplanData && Store.unitplanData[unitplanId.value].tour_id)) {
  Store.Navigator.interior = true;
  showbottomNav.value = true;
}
if (Store.unitplanData && Store.unitplanData[unitplanId.value].exterior_type) {
  Store.Navigator.exterior = true;
  showbottomNav.value = true;
}
function setCurrentActive (tab, imageId, flag) {
  currentImageId.value = imageId || null;
  activeNavigatorTab.value = tab;
  if (route.params.unitplanId){
    router.push({ name: "unit.Child", query: { ...route.query, type: tab } });
  } else {
    router.push({name: "unitview", query: { ...route.query, type: tab }});
  }
  let ga_fields =  buildAnalyticsFields();

  if (tab === 'interior') {
    enterVR();
    if (flag){
      ga_fields ={
        ...ga_fields,
        tour_id: Store.unitplanData[Store.unitData[unitId.value]?.unitplan_id]?.tour_id,
        tour_name: Store.unitplanData[unitplanId.value]?.name? Store.unitplanData[unitplanId.value]?.name:Store.unitplanData[unitplanId.value] ?.name,
      };
      Googleanalytics('tour_clicked', ga_fields);
    }
  } else if (tab === 'exterior') {
    if (flag){
      ga_fields ={
        ...ga_fields,
        unit_id: Store.allUnitCardData[unitId.value]?._id,
        unit_name: Store.allUnitCardData[unitId.value]?.name,
      };
      Googleanalytics('exterior_clicked', ga_fields);
    }
    if (Store.unitplanData && Store.unitplanData[unitplanId.value].exterior_type === 'scene') {
      Store.getRotatableFrames(route.params.projectId, props.organizationId, 'rotatable_image_frame', Store.unitplanData[unitplanId.value].scene_id).then((res) => {
        scene_data.value = res;
        showScene.value = true;
      });
    } else {
      if (filteredList.value.length === 0 && Store.galleryList) {
        Store.unitplanData[unitplanId.value].gallery_id.forEach((item) => {
          filteredList.value.push(Store.galleryList[item]);
        });
        activeTab.value = route.query.current_gallery;
      }
      showGallery.value = true;
      selectedMediaIndex.value = 0 + 1; // Initial
    }
  } else {
    showGallery.value = false;
    showScene.value = false;
    if (flag){
      Googleanalytics('unitplan_clicked', ga_fields);
    }
  }
}

if (!Store.galleryList) {
  Store.getGallery(route.params.organizationId, route.params.projectId);
}
watch(
  () => [Store.galleryList],
  () => {
    if (route.query.type === 'exterior') {
      if (filteredList.value.length === 0 && Store.galleryList && Store.unitplanData && Store.unitplanData[unitplanId.value].exterior_type === 'gallery') {
        Store.unitplanData[unitplanId.value].gallery_id.forEach((item) => {
          filteredList.value.push(Store.galleryList[item]);
        });
        showGallery.value = true;
        selectedMediaIndex.value = 0 + 1; // Initial
        activeTab.value = route.query.current_gallery;
      }
    }
  });
function movetoprevscene (){
  router.push({name: 'projectScene', query: {building_id: route.query.building_id, floor_id: route.query.floor_id, community_id: route.query.community_id, status: availUnitOrgs.includes(route.params.organizationId)?'available':""}});
}

function handleLowResLoaded () {
  lowResLoaded.value = true;
}

const handlePrev = () => {
  const currentIndex = Store.unitplanData[unitplanId.value].floor_unitplans.indexOf(currentFloorUnitplandId.value);
  if (currentIndex > 0) {
    const prevIndex = currentIndex - 1;
    currentFloorUnitplandId.value = Store.unitplanData[unitplanId.value].floor_unitplans[prevIndex];
  }
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  swiperContainer.slidePrev();
};

const handleNext = () => {
  const currentIndex = Store.unitplanData[unitplanId.value].floor_unitplans.indexOf(currentFloorUnitplandId.value);
  if (currentIndex < Store.unitplanData[unitplanId.value].floor_unitplans.length - 1) {
    const nextIndex = currentIndex + 1;
    currentFloorUnitplandId.value = Store.unitplanData[unitplanId.value].floor_unitplans[nextIndex];
  }
  const swiperContainer = document.getElementById('overflowSliderRef').swiper;
  swiperContainer.slideNext();
};

const handleSelection = (val) => {
  currentFloorUnitplandId.value = val;
};

watch(() => currentFloorUnitplandId.value, (newVal) => {
  currentFloorUnitplandId.value = newVal;
});

watch(() => [Store.unitplanData[unitplanId.value], activeFloorTab.value], ([newVal, activeTab]) => {
  if (isRealValue(newVal)) {
    const unitplanArray = newVal.floor_unitplans;

    if (unitplanArray){
      // Set activeFloorTab to the first element if it's empty
      let currentTab = activeTab;
      if (!currentTab) {
        currentTab = unitplanArray[0];
      } else {
        currentTab = currentFloorUnitplandId.value;
      }
      if (activeFloorTab.value !== currentTab) {
        activeFloorTab.value = currentTab;
      }
      const unitArray = unitplanArray.map((key) => {
        const unitplandata = Store.unitplanData[key];
        return {
          name: unitplandata.name,
          measurement: unitplandata.measurement,
          measurement_type: unitplandata.measurement_type,
        };
      });

      unitPlanArrayData.value = unitArray;
    }

  }
}, { immediate: true });

function updatedImageId (id) {
  currentImageId.value = id;
}

watch(() => route.query.type, (newVal) => {
  activeNavigatorTab.value = newVal;
});

function findParentId (imageId, data) {
  const result = data.map((id) => Store.unitplanData[id] || `No data for ID: ${id}`);
  const groupedImageIds = {};

  result.forEach((unitData) => {
    const parentId = unitData._id;
    groupedImageIds[parentId] = [];
    if (unitData.hotspots){
      Object.values(unitData.hotspots).forEach((hotspot) => {
        if (hotspot.image_id) {
          groupedImageIds[parentId].push(hotspot.image_id);
        } else if (hotspot.label_id){
          groupedImageIds[parentId].push(hotspot.label_id);
        }
      });
    }
  });

  for (const parentId in groupedImageIds) {
    if (groupedImageIds[parentId].includes(imageId)) {
      currentFloorUnitplandId.value = parentId;
      break;
    }
  }
}

watch(() => currentImageId.value, (newVal) => {
  currentImageId.value = newVal;
  const floorUnitPlans = Store.unitplanData[unitplanId.value]?.floor_unitplans;
  if (floorUnitPlans && floorUnitPlans.length > 0) {
    findParentId(newVal, floorUnitPlans);
    defaultIndex.value = floorUnitPlans.indexOf(currentFloorUnitplandId.value);
    if (defaultIndex.value !== -1) {
      document.getElementById('overflowSliderRef')?.swiper.slideTo(defaultIndex.value, 0);
    }
  }
});

onUnmounted(() => {
  Store.hideLogo = false;
});

const updateActiveNavigatorTab = (tab) => {
  activeNavigatorTab.value = tab;
};
</script>

<template>
  <NavBar @handle-navigation="movetoprevscene" />
  <RegisterInterest
    v-if="showRegisterInterest"
    :unitId="unitId"
    :unitName="Store.allUnitCardData[unitId].name"
    @handle-close="() => showRegisterInterest = false"
  />
  <div
    id="maincompview"
    class="flex flex-col h-full w-inherit"
  >
    <TabsView
      v-if="Store.isMobile || Store.isLandscape"
      ref="tabsView"
      :navigator="{
        'unitplan': true,
        'interior': ((Store.unitData && Store.unitData[unitId].tour_id) || (Store.unitplanData && Store.unitplanData[unitplanId].tour_id)) ? true : false,
        'exterior': Store.unitplanData && Store.unitplanData[unitplanId].exterior_type ? true : false
      }"
      class="bg-gray-700 w-full absolute z-[2]"
      :class="Store.isLandscape?'top-[2.5rem] ':'top-[3.7rem] '"
      :default-id="activeNavigatorTab"
      @handle-selection="setCurrentActive"
    />

    <div :class="[Store.isMobile ? 'h-full w-full flex flex-col justify-start items-start' : 'h-full relative sm:w-full sm:fixed sm:flex-auto',]">
      <div
        v-if="Store.isMobile || Store.isLandscape"
        class="w-full flex-shrink-0 flex-grow-0 bg-transparent"
      />

      <div
        v-if="activeNavigatorTab === 'unitplan'"
        style="background-image: url('/assets/bg for unit plan fhd.png')"
        class="h-full bg-cover bg-center w-full relative"
        :class="Store.isLandscape?'mt-5':''"
      >
        <CircularLoader v-if="!Store.unitplanData[unitplanId]" />

        <!-- Picture --><!-- flat -->
        <div
          v-if="Store.unitplanData[unitplanId] ? Store.unitplanData[unitplanId].unit_type !=='villa' : false"
          class="w-full h-[80%] md:h-[92.5%] relative flex justify-center items-center mobile-screen "
        >
          <div
            class="flex items-center justify-center relative"
            style="width: inherit; height: inherit"
          >
            <HotSpotViewer
              :highResUrl="Store.unitplanData[unitplanId].image_url"
              :lowResUrl="Store.unitplanData[unitplanId].thumbnail"
              :hotSpots="Store.unitplanData[unitplanId].hotspots"
              :activeNavigatorTab="activeNavigatorTab"
              @active-navigator-tab-emit="updateActiveNavigatorTab"
              @set-current-active="setCurrentActive"
            />
          </div>
        </div>
        <!-- villa, villa_floor -->
        <div
          v-else
          class="w-full h-[80%] md:h-[92.5%] relative flex justify-center items-center mobile-screen"
        >
          <div
            class="flex items-center justify-center relative"
            style="width: inherit; height: inherit"
          >
            <HotSpotViewer
              v-if="currentFloorUnitplandId"
              :highResUrl="Store.unitplanData[currentFloorUnitplandId].image_url"
              :currentFloorUnitplandId="currentFloorUnitplandId"
              :lowResUrl="Store.unitplanData[currentFloorUnitplandId].thumbnail"
              :hotSpots="Store.unitplanData[currentFloorUnitplandId].hotspots"
              :activeNavigatorTab="activeNavigatorTab"
              @active-navigator-tab-emit="updateActiveNavigatorTab"
              @set-current-active="setCurrentActive"
            />
          </div>
        </div>

        <ButtonGroup
          v-if="activeNavigatorTab === 'unitplan' && Store.unitplanData[unitplanId] ? Store.unitplanData[unitplanId].unit_type === 'villa' : false"
          :floorNames="Store.unitplanData[unitplanId].floor_unitplans"
          :floorData="Store.unitplanData"
          :activeTab="activeFloorTab ? activeFloorTab : Store.unitplanData[unitplanId].floor_unitplans[0]"
          :class="Store.isLandscape ?'absolute !bottom-10' :'absolute top-[8rem] sm:top-auto'"
          @set-current-floor="setCurrentFloor"
        />
      </div>
      <div
        v-if="!Store.isMobile && !Store.isLandscape"
        class="absolute pb-8 !bottom-0 left-8 transform  z-[5] flex flex-col gap-5"
      >
        <BottomNavigation
          v-if="Store.unitplanData && Store.unitplanData[unitplanId].exterior_type || ((Store.unitData && unitId && Store.unitData[unitId].tour_id) || (Store.unitplanData && unitplanId && Store.unitplanData[unitplanId].tour_id))"
          :navigator="{
            'unitplan': true,
            'interior': ((Store.unitData && unitId && Store.unitData[unitId].tour_id) || (Store.unitplanData && Store.unitplanData[unitplanId].tour_id)) ? true : false,
            'exterior': Store.unitplanData && Store.unitplanData[unitplanId].exterior_type ? true : false
          }"
          :activeTab="activeNavigatorTab"
          @set-current-active="setCurrentActive"
        />
      </div>

      <!--unitplan preview-->
      <div
        v-if="activeNavigatorTab === 'interior'"
        class="md:w-[18vw] md:max-w-[20vw]"
        :class="[
          'absolute bottom-0 transform flex flex-col gap-5 w-full',
          Store.isMobile
            ? (showMiniMap
              ? 'z-[10] !h-[45%]'
              : 'mb-40 right-3'
            )
            : (showMiniMap || Store.isLandscape
              ? 'z-[1] mb-14 right-28 xl:right-8 !h-[30%] !w-[18vw]'
              : 'z-[1] mb-14 right-28 xl:right-8'
            ),
          !Store.isMobile && !showMiniMap ? '' : ''
        ]"
      >
        <!-- Minimap icon -->
        <div
          v-if="!showMiniMap"
          class=" w-10 h-10 md:w-8 md:h-8 bg-secondary absolute rounded-[2rem] flex !items-center !justify-center backdrop-blur-[3.125rem] bottom-0 bg-opacity-60"
          :class="Store.isLandscape ? '-right-[1.5rem] -bottom-[50px] -mr-2':'-right-[4px]'"
          @click="showMiniMap = !showMiniMap"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            class="w-5 h-5"
          >
            <path
              d="M9.5625 6.4375H9.56812"
              :stroke="!showMiniMap ? 'var(--secondaryText)' : ''"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9.5625 4.25C10.7463 4.25 11.75 5.25824 11.75 6.48144C11.75 7.72412 10.73 8.59619 9.78781 9.18919C9.71913 9.22906 9.6415 9.25 9.5625 9.25C9.4835 9.25 9.40587 9.22906 9.33719 9.18919C8.39675 8.59038 7.375 7.72844 7.375 6.48144C7.375 5.25824 8.37869 4.25 9.5625 4.25Z"
              :stroke="!showMiniMap ? 'var(--secondaryText)' : ''"
            />
            <path
              d="M2.0625 8C2.0625 5.20104 2.0625 3.80156 2.93202 2.93202C3.80156 2.0625 5.20104 2.0625 8 2.0625C10.7989 2.0625 12.1984 2.0625 13.068 2.93202C13.9375 3.80156 13.9375 5.20104 13.9375 8C13.9375 10.7989 13.9375 12.1984 13.068 13.068C12.1984 13.9375 10.7989 13.9375 8 13.9375C5.20104 13.9375 3.80156 13.9375 2.93202 13.068C2.0625 12.1984 2.0625 10.7989 2.0625 8Z"
              :stroke="!showMiniMap ? 'var(--secondaryText)' : ''"
            />
            <path
              d="M11.125 13.625L2.375 4.875"
              :stroke="!showMiniMap ? 'var(--secondaryText)' : ''"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M6.75 9.25L3 13"
              :stroke="!showMiniMap ? 'var(--secondaryText)' : ''"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div
          v-if="showMiniMap"
          class="flex border border-none rounded-xl bg-secondary py-4 px-4 relative h-full justify-center items-center"
          :class="Store.isMobile ? 'flex-col-reverse justify-around' : ''"
        >
          <div
            class="absolute top-2 right-2 cursor-pointer"
            @click="closeMap"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="19"
              viewBox="0 0 18 19"
              fill="none"
            >
              <path
                d="M13.5 14.25L4.5 5.25"
                :stroke="showMiniMap ? 'var(--secondaryText)' : ''"
                stroke-width="1.18125"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M13.5 5.25L4.5 14.25"
                :stroke="showMiniMap ? 'var(--secondaryText)' : ''"
                stroke-width="1.18125"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div
            v-if="currentFloorUnitplandId"
            class="flex justify-center items-center border border-[#374151] z-40  rounded bg-secondary"
            :class="Store.isMobile ? 'flex-row h-[15%] w-[70%]' : 'flex-col h-[70%] w-[15%]'"
          >
            <button
              v-if="Store.unitplanData[unitplanId].unit_type === 'villa'"
              class="flex items-center justify-center"
              :class="[Store.isMobile ? 'w-[20%] h-full rotate-[-90deg]' : 'h-[20%] w-full']"
              :disabled="Store.unitplanData[unitplanId]?.floor_unitplans.indexOf(currentFloorUnitplandId) === 0"
              @click="handlePrev"
            >
              <svg
                class="w-8 h-fit"
                viewBox="0 0 16 16"
                fill="white"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4 10.06L4.94 11L8 7.94667L11.06 11L12 10.06L8 6.06L4 10.06Z"
                  fill="#D8DBDF"
                />
              </svg>
            </button>
            <OverflowSlider
              id="overflowSliderRef"
              :direction="Store.isMobile ? 'horizontal' : 'vertical'"
              :slides-per-view="slideView"
              :initial-slide="defaultIndex"
              :class="Store.isMobile ? 'w-[60%] h-full' : 'h-[60%] w-full'"
              @swiper-slide-change="handleSwiperSlideChange"
            >
              <template #options>
                <swiper-slide
                  v-for="(item, index) in Store.unitplanData[unitplanId].floor_unitplans"
                  :key="item"
                  :class="[
                    (currentFloorUnitplandId === item ? 'bg-primary text-primaryText ' : 'text-secondaryText hover:text-secondaryText bg-secondary'),
                    'aspect-square bg-opacity-75 shadow-inner backdrop-blur-[7.50px] justify-center items-center inline-flex cursor-pointer !mb-0 border border-[#374151]'
                  ]"
                  class="text-primaryText"
                  @click="handleSelection(item)"
                >
                  <p class="text-inherit  font-medium text-center uppercase lg:text-base text-[10px] ">
                    {{ index + 1 }}
                  </p>
                </swiper-slide>
              </template>
            </OverflowSlider>
            <button
              v-if="Store.unitplanData[unitplanId].unit_type === 'villa'"
              class="flex items-center justify-center"
              :class="[Store.isMobile ? 'w-[20%] h-full rotate-[-90deg]' : 'h-[20%] w-full']"
              :disabled="Store.unitplanData[unitplanId]?.floor_unitplans.indexOf(currentFloorUnitplandId) === Store.unitplanData[unitplanId.value]?.floor_unitplans.length - 1"
              @click="handleNext"
            >
              <svg
                class="w-8 h-fit"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 6.94L11.06 6L8 9.05333L4.94 6L4 6.94L8 10.94L12 6.94Z"
                  fill="#D8DBDF"
                />
              </svg>
            </button>
          </div>
          <HotSpotViewer
            v-if="Store.unitplanData[unitplanId] ? Store.unitplanData[unitplanId].unit_type !=='villa' : false"
            :highResUrl="Store.unitplanData[unitplanId].image_url"
            :lowResUrl="Store.unitplanData[unitplanId].thumbnail"
            :hotSpots="Store.unitplanData[unitplanId].hotspots"
            :activeNavigatorTab="activeNavigatorTab"
            :currentImageId="currentImageId"
            @active-navigator-tab-emit="updateActiveNavigatorTab"
            @set-current-active="setCurrentActive"
            @low-res-loaded="handleLowResLoaded"
            @current-image-id-emit="updatedImageId"
          />
          <HotSpotViewer
            v-if="currentFloorUnitplandId"
            :highResUrl="Store.unitplanData[currentFloorUnitplandId].image_url"
            :lowResUrl="Store.unitplanData[currentFloorUnitplandId].thumbnail"
            :hotSpots="Store.unitplanData[currentFloorUnitplandId].hotspots"
            :activeNavigatorTab="activeNavigatorTab"
            :currentImageId="currentImageId"
            @active-navigator-tab-emit="updateActiveNavigatorTab"
            @set-current-active="setCurrentActive"
            @low-res-loaded="handleLowResLoaded"
            @current-image-id-emit="updatedImageId"
          />
        </div>
      </div>

      <!--gallery data-->
      <unitPlanScene
        v-if="showGallery && Store.galleryList && !showScene && activeNavigatorTab === 'exterior'"
        :filteredList="filteredList"
        :selectedMediaIndex="selectedMediaIndex"
        :isVisible="isVisible"
        :activeTab="activeTab"
        @selected-media-index="selectedMediaIndex"
      />

      <div
        v-if="activeNavigatorTab === 'interior' && showInterior"
        :class="[Store.isMobile ? 'flex-grow w-full' : 'h-full']"
      >
        <TourView
          :showInterior="showInterior"
          :listOfVrTourData="Store.listOfVrTourData[showInterior]"
          :currentImageId="currentImageId"
          @updated-image-id="updatedImageId"
          @current-image-id-emit="updatedImageId"
        />
      </div>
      <unitPlanScene
        v-if="activeNavigatorTab === 'exterior' && showScene"
        :showScene="showScene"
        :scene_data="scene_data"
        @remove-loader="removeLoader = true"
      />
    </div>
    <CircularLoader v-if="activeNavigatorTab === 'exterior' && !removeLoader" />
    <div
      v-if="Store.sidebarOptions[route.params.projectId]"
      class="flex-shrink-0 sm:z-auto z-[2]"
    >
      <NewAleSideBar
        :sidebarList="Store.sidebarOptions[route.params.projectId]"
        @select-option="moveToLocation"
      />
    </div>
  </div>

  <!-- unitplan data NOT NEEDED -->
  <div
    v-if="showUnitplanCard"
    class="fixed top-0 floordataa z-20 left-0 w-screen h-screen"
    clickaway="true"
    @click="closeModal"
  >
    <FloorPlanCard
      style="position: absolute;right: 1.5rem;top: 8rem;"
      :unitId="Store.allUnitCardData[showUnitplanCard]._id"
      :title="Store.allUnitCardData[showUnitplanCard].name"
      :status="Store.allUnitCardData[showUnitplanCard].status"
      :price="Store.allUnitCardData[showUnitplanCard].price"
      :measurement="Store.allUnitCardData[showUnitplanCard].measurement"
      :bedrooms="Store.allUnitCardData[showUnitplanCard].bedroom"
      :currency-title="Store.allUnitCardData[showUnitplanCard].currency"
      :is-show-buttons="true"
      button-view="floorplanview"
      :isMobile="Store.isMobile"
      :favUnits="Store.favoritesData"
      :hideStatus="Store.hideStatus"
      @show-unitplan="gotoUnit"
    />
  </div>
  // cardddddddd
  <div
    v-if="Object.keys(Store.allUnitCardData).length > 0 && unitId"
    class="fixed z-[2]"
    :class="[
      Store.isMobile
        ? 'bottom-2 !p-0 w-full'
        : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0
          ? 'right-24 bottom-6'
          : Store.isLandscape
            ? '!right-8 bottom-6'
            : 'md:right-8 md:bottom-auto top-auto bottom-16 md:top-24'
    ]"
  >
    <NewFloorPlanCard
      :class="Store.isMobile ? 'slide-animation' : 'slide'"
      :bedroomNumber="Store.unitplanData && Store.unitplanData[unitplanId].bedrooms"
      :unitType="Store.unitplanData && Store.unitplanData[unitplanId].unit_type"
      :location="route.query.community_id &&Object.keys(Store.communityData).length?Store.communityData[route.query.community_id].name:Store.buildingData[buildingId]?.name"
      :status="Store.allUnitCardData[unitId].status"
      :unitPlans="unitPlanArrayData"
      :unitName="Store.allUnitCardData[unitId].name"
      :isMobile="Store.isMobile"
      :hideClose="false"
      :styleType="Store.unitplanData && Store.unitplanData[unitplanId].style ? Store.unitplanData[unitplanId].style: null"
      :price="Store.allUnitCardData[unitId].price"
      :maxPrice="Store.allUnitCardData[unitId].max_price"
      :measurement="Store.allUnitCardData[unitId].measurement"
      :measurementType="Store.allUnitCardData[unitId].measurement_type"
      :balcony="!Store.unitplanData[unitplanId]?.unit_type || Store.unitplanData[unitplanId]?.unit_type === 'flat' && Store.allUnitCardData[unitId].balcony_measurement !== 0 ? Store.allUnitCardData[unitId].balcony_measurement : Store.unitplanData[unitplanId].balcony_measurement"
      :balconyType="!Store.unitplanData[unitplanId]?.unit_type || Store.unitplanData[unitplanId]?.unit_type === 'flat' && Store.allUnitCardData[unitId].balcony_measurement_type !== 0 ? Store.allUnitCardData[unitId].balcony_measurement_type : Store.unitplanData[unitplanId].balcony_measurement_type"
      :suiteArea="Store.allUnitCardData[unitId]?.suite_area ? Store.allUnitCardData[unitId]?.suite_area : Store.unitplanData[unitplanId]?.suite_area"
      :suiteAreaType="Store.allUnitCardData[unitId]?.suite_area_type ? Store.allUnitCardData[unitId]?.suite_area_type : Store.unitplanData[unitplanId]?.suite_area_type"
      :currency-title="Store.allUnitCardData[unitId].currency"
      :totalArea="!Store.unitplanData[unitplanId]?.unit_type || Store.unitplanData[unitplanId]?.unit_type === 'flat'? (Store.allUnitCardData[unitId].measurement !== 0 ? Store.allUnitCardData[unitId].measurement : Store.unitplanData[unitplanId].measurement) :Store.unitplanData[unitplanId].measurement"
      :unitId="unitId"
      :favUnits="Store.favoritesData"
      :ctaLink="Store.allUnitCardData[unitId]?.cta_link"
      :hideStatus="Store.hideStatus"
      :is_commercial="Store.unitplanData[Store.allUnitCardData[unitId]?.unitplan_id]?.is_commercial && Store.unitplanData[Store.allUnitCardData[unitId]?.unitplan_id]?.is_commercial !== undefined ? Store.unitplanData[Store.allUnitCardData[unitId]?.unitplan_id]?.is_commercial : false"
      class="shadow-none"
      @handle-cta="handleCta"
      @handle-favorite="Store.updateAddRemovefavorites(Store.allUnitCardData[unitId])"
      @closemodal="isVisible = !isVisible"
    />
  </div>
</template>
<style scoped>

/* Your other styles - KEEP THESE */
.plan-modal {
  background-image: url('/assets/unitplanbg.jpg');
  background-size: cover;
  background-repeat: no-repeat;
}

.slide {
  transform: translateX(100%);
  animation: slideIn 0.5s forwards;
}

 @keyframes slideIn {
   from {
     transform: translateX(100%);
   }

   to {
     transform: translateX(0);
   }
 }

@media screen and (max-width: 430px) {
  .slide-animation {
    animation: popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
  }
}

@keyframes popup {
  0% {
    bottom: -24em;
  }

  100% {
    @apply md:bottom-0;
  }
}

.img-landscape {
  @apply h-auto max-h-[100%] sm:w-[60%] w-[95%] md:py-8 object-contain ;
}

@media only screen and (max-width: 640px) {
  .mobile-screen {
    transform: translate(0px, 3rem);
    scale: 0.9;
  }
}
</style>
